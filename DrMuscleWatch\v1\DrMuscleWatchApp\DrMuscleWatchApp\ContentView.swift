//
//  ContentView.swift
//  DrMuscleWatchApp
//
//  Created on May 1, 2024
//

import SwiftUI
import CoreData

struct ContentView: View {
    // Access the managed object context
    @Environment(\.managedObjectContext) private var viewContext

    // Fetch request for workouts
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Workout.timestamp, ascending: false)],
        animation: .default)
    private var workouts: FetchedResults<Workout>

    var body: some View {
        // Main app content - no authentication needed for watchOS
        HomeView()
    }
}

/// Home view for the watch app
struct HomeView: View {
    // Access the managed object context
    @Environment(\.managedObjectContext) private var viewContext

    // Fetch request for workouts
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Workout.timestamp, ascending: false)],
        animation: .default)
    private var workouts: FetchedResults<Workout>

    var body: some View {
        VStack {
            Text("Dr. Muscle")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)

            Spacer().frame(height: 20)

            Text("Watch App")
                .font(.headline)
                .foregroundColor(.gray)

            // Display the number of workouts in Core Data (for testing)
            Text("\(workouts.count) workouts stored")
                .font(.caption)
                .foregroundColor(.gray)
                .padding(.top, 10)

            Text("Ready to track workouts!")
                .font(.body)
                .foregroundColor(.white)
                .padding(.top, 10)
        }
        .padding()
    }
}

#Preview {
    ContentView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
